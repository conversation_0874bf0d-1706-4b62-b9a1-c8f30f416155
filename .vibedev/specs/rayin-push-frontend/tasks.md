# Rayin-Push Frontend Implementation Tasks

## 项目设置和基础架构

### 1. 项目初始化和配置
- [x] 1.1 创建 Next.js 14 项目并配置基础依赖
  - 使用 pnpm 创建 Next.js 14 App Router 项目
  - 安装必要依赖：shadcn/ui、zustand、i18next、framer-motion、tailwindcss
  - 配置 TypeScript、ESLint、Prettier
  - 设置基础的 next.config.js 配置
  - **对应需求**: 技术架构需求 9.1-9.8

- [x] 1.2 配置 Shadcn/ui 组件库
  - 初始化 shadcn/ui 配置
  - 创建 components.json 配置文件
  - 安装基础 UI 组件：button、card、table、dialog、form、input
  - 配置 TailwindCSS 样式系统
  - **对应需求**: 技术架构需求 9.2, 9.4

### 2. 国际化系统设置
- [x] 2.1 实现 i18next 国际化配置
  - 创建 i18next 配置文件和中间件
  - 设置动态路由 [locale] 结构
  - 创建翻译资源文件结构 (zh/en)
  - 实现语言切换 Hook 和组件
  - **对应需求**: 国际化需求 7.1-7.5

- [x] 2.2 创建翻译资源文件
  - 创建各模块的翻译文件：common、dashboard、config、channels、logs、limits、users
  - 实现翻译文件的类型定义
  - 测试语言切换功能
  - **对应需求**: 国际化需求 7.4

### 3. 状态管理架构
- [x] 3.1 建立 Zustand 状态管理基础
  - 创建全局状态 store (主题、语言、侧边栏状态)
  - 实现状态持久化配置
  - 创建状态管理的 TypeScript 类型定义
  - **对应需求**: 技术架构需求 9.3

- [x] 3.2 创建模拟数据系统
  - 创建各模块的模拟数据：dashboard、config、channels、logs、limits、users
  - 实现模拟 API 函数用于数据操作
  - 确保模拟数据的真实性和多样性
  - **对应需求**: 数据模拟需求 10.1-10.7

## 布局和导航系统

### 4. 主要布局组件
- [x] 4.1 创建应用主布局框架
  - 实现根布局组件 (app/layout.tsx)
  - 创建带国际化的页面布局 (app/[locale]/layout.tsx)
  - 实现错误边界和加载状态组件
  - **对应需求**: 技术架构需求 9.7

- [x] 4.2 实现侧边栏导航组件
  - 创建响应式侧边栏组件
  - 实现菜单项组件和导航逻辑
  - 添加侧边栏折叠/展开功能
  - 实现移动端导航适配
  - **对应需求**: 响应式需求 8.1-8.5

- [x] 4.3 创建顶部导航栏
  - 实现面包屑导航组件
  - 创建用户菜单和设置下拉组件
  - 添加语言切换器和主题切换器
  - 实现移动端导航切换按钮
  - **对应需求**: 国际化需求 7.1, 响应式需求 8.2

### 5. 通用组件库
- [x] 5.1 创建数据表格组件
  - 实现可复用的数据表格组件
  - 添加排序、筛选、分页功能
  - 实现移动端表格响应式显示
  - 添加加载和空状态显示
  - **对应需求**: 响应式需求 8.4

- [x] 5.2 实现搜索和筛选组件
  - 创建搜索输入框组件
  - 实现防抖搜索功能
  - 创建日期范围选择器
  - 实现状态筛选下拉组件
  - **对应需求**: 请求日志需求 4.2, 4.7

## 仪表盘模块实现

### 6. 仪表盘页面和组件
- [x] 6.1 创建仪表盘统计卡片组件
  - 实现今日请求数量统计卡片
  - 实现总请求数量统计卡片  
  - 实现成功率百分比统计卡片
  - 添加数据变化趋势显示
  - 实现加载状态和错误状态
  - **对应需求**: 仪表盘需求 1.1-1.3

- [x] 6.2 实现最近请求列表组件
  - 创建最近10个请求的列表组件
  - 显示请求时间、接口名称、状态信息
  - 实现点击请求列表项跳转到日志详情功能
  - 添加请求状态图标和样式
  - **对应需求**: 仪表盘需求 1.5

- [x] 6.3 创建趋势图表组件
  - 使用图表库实现最近请求趋势图
  - 添加时间范围选择功能 (24小时、7天、30天)
  - 添加图表交互和悬停效果
  - **对应需求**: 仪表盘需求 1.4

- [x] 6.4 集成仪表盘页面
  - 创建仪表盘主页面
  - 集成所有仪表盘组件
  - 实现数据加载和状态管理
  - 添加响应式布局适配
  - **对应需求**: 仪表盘需求 1.1-1.9

## 接口配置管理模块

### 7. 配置管理页面
- [x] 7.1 创建配置列表页面
  - 实现配置列表展示页面 (配置名称、令牌、描述、请求方式、状态、时间)
  - 添加配置项的基本信息显示
  - 实现创建、编辑、删除操作按钮
  - 添加禁用/启用配置功能
  - 实现批量操作和状态切换功能
  - **对应需求**: 接口配置需求 2.1, 2.3

- [x] 7.2 实现配置表单组件
  - 创建配置创建/编辑表单
  - 实现表单验证和错误处理
  - 添加配置名称、描述等基本信息输入
  - 实现推送渠道选择功能
  - **对应需求**: 接口配置需求 2.2, 2.4

### 8. 消息解析规则配置
- [x] 8.1 创建解析规则编辑器组件
  - 实现 GET 请求参数映射配置界面 (URL 参数映射)
  - 创建 POST 请求类型选择组件 (form-urlencoded, multipart, json, plain)
  - 实现键值对映射编辑器 (sourceField -> targetField)
  - **对应需求**: 接口配置需求 2.5 (GET、POST form-urlencoded、multipart/form-data)

- [x] 8.2 实现 JSON 字段提取配置
  - 创建 JSON 路径编辑器 (支持嵌套字段访问)
  - 实现嵌套字段映射配置 (如 info.address -> userAddress)
  - 添加 JSON 结构预览和测试功能
  - 实现字段类型检查和验证
  - 支持多层级字段提取和重命名
  - **对应需求**: 接口配置需求 2.5 (application/json 多层级字段提取)

- [ ] 8.3 创建正则表达式规则编辑器
  - 实现 text/plain 正则解析配置
  - 创建正则表达式测试工具和实时预览
  - 添加捕获组配置和命名功能
  - 实现正则规则保存和管理
  - 支持多个正则模式组合使用
  - **对应需求**: 接口配置需求 2.5 (text/plain 正则表达式解析)

## 通知渠道模块

### 10. 渠道配置管理
- [ ] 10.1 创建渠道列表和表单
  - 实现通知渠道列表页面 (显示渠道名称、类型、请求方式、状态、时间)
  - 创建渠道创建/编辑表单
  - 实现渠道类型选择 (微信机器人/飞书机器人/自定义 Webhook)
  - 添加渠道状态管理功能 (启用/禁用)
  - **对应需求**: 通知渠道需求 3.1, 3.2, 3.7

- [ ] 10.2 实现内置模板系统
  - 创建微信机器人模板配置 (预设 URL 和消息格式)
  - 实现飞书机器人模板配置 (预设 URL 和消息格式)
  - 添加模板选择和预设功能
  - 实现模板参数自动配置
  - **对应需求**: 通知渠道需求 3.3

### 11. 自定义 Webhook 配置
- [ ] 11.1 创建 Webhook 配置界面
  - 实现 HTTP 方法选择 (GET/POST)
  - 创建请求头配置编辑器 (键值对形式)
  - 实现请求体格式选择 (form-urlencoded, multipart, json, plain)
  - 添加 URL 参数配置功能 (用于 GET 请求)
  - **对应需求**: 通知渠道需求 3.4, 3.5 (多种请求方式支持)

- [ ] 11.2 实现模板变量系统
  - 创建变量编辑器支持 ${xxx} 语法 (从接口配置提取的变量)
  - 实现内置变量功能 (${data} 完整数据, ${now} 当前时间)
  - 添加简单运算表达式支持 (${x1 + x2})
  - 实现 ${if/else} 条件判断语法
  - 添加变量预览和测试功能
  - **对应需求**: 通知渠道需求 3.6 (模板变量替换、内置变量、运算、条件判断)

- [ ] 11.3 添加渠道测试和管理功能
  - 实现渠道配置测试功能
  - 创建测试消息发送界面
  - 显示测试结果和响应数据
  - 添加渠道编辑和删除功能
  - 实现渠道配置验证
  - **对应需求**: 通知渠道需求 3.7

## 请求日志模块

### 12. 日志列表和详情
- [ ] 12.1 创建请求日志列表页面
  - 实现日志列表展示页面
  - 显示基本日志信息 (接口名、状态、耗时、时间)
  - 添加状态图标和颜色标识
  - 实现分页加载功能
  - **对应需求**: 请求日志需求 4.1, 4.8

- [ ] 12.2 实现日志筛选功能
  - 创建时间范围筛选器
  - 实现状态筛选功能
  - 添加接口名称筛选
  - 实现日志搜索功能
  - **对应需求**: 请求日志需求 4.2, 4.7

- [ ] 12.3 创建日志详情页面
  - 实现日志详情展示页面
  - 显示各渠道发送结果列表
  - 展示原始数据和处理后数据
  - 添加错误信息详细显示
  - **对应需求**: 请求日志需求 4.3, 4.4, 4.5

- [ ] 12.4 实现日志导出功能
  - 创建日志导出功能
  - 支持 CSV、JSON 格式导出
  - 实现批量导出和筛选导出
  - 添加导出进度和状态提示
  - **对应需求**: 请求日志需求 4.6

## 请求限制模块

### 13. 限制规则管理
- [ ] 13.1 创建速率限制管理
  - 实现速率限制规则列表
  - 创建速率限制配置表单
  - 添加时间窗口和请求数配置
  - 实现多条规则管理功能
  - **对应需求**: 请求限制需求 5.1, 5.2

- [ ] 13.2 实现 IP 访问限制
  - 创建 IP 限制规则配置
  - 实现单个 IP 和 IP 段输入
  - 添加白名单和黑名单切换
  - 实现 IP 规则验证功能
  - **对应需求**: 请求限制需求 5.4, 5.5

- [ ] 13.3 创建限制统计和管理
  - 显示当前生效的限制规则
  - 实现规则启用/禁用功能
  - 添加触发限制的统计信息
  - 实现规则编辑和删除功能
  - **对应需求**: 请求限制需求 5.6, 5.7, 5.8, 5.9

## 用户管理模块

### 14. 用户管理功能
- [ ] 14.1 创建用户管理列表
  - 实现用户列表展示 (仅管理员可见)
  - 显示用户基本信息和状态
  - 添加创建时间和最后登录时间
  - 实现用户搜索和筛选功能
  - **对应需求**: 用户管理需求 6.1, 6.7

- [ ] 14.2 实现用户创建和编辑
  - 创建用户创建/编辑表单
  - 实现用户名、邮箱、密码输入
  - 添加角色选择功能 (admin/user)
  - 实现用户信息验证和保存
  - **对应需求**: 用户管理需求 6.2, 6.3, 6.4

- [ ] 14.3 实现用户权限和保护
  - 实现密码重置功能
  - 添加用户启用/禁用功能
  - 实现第一个管理员保护逻辑
  - 添加管理员自删防护功能
  - **对应需求**: 用户管理需求 6.5, 6.6, 6.8, 6.9

- [ ] 14.4 添加用户操作日志
  - 实现用户操作记录功能
  - 创建操作日志展示界面
  - 添加操作类型和时间记录
  - 实现日志筛选和搜索
  - **对应需求**: 用户管理需求 6.10

## 响应式和主题系统

### 15. 响应式适配完善
- [ ] 15.1 实现移动端适配
  - 优化移动端布局和导航
  - 实现移动端表格和表单适配
  - 添加触摸友好的交互元素
  - 测试各个页面的移动端显示
  - **对应需求**: 响应式需求 8.1-8.5

- [ ] 15.2 完善主题系统
  - 实现深色/浅色主题切换
  - 添加主题状态持久化
  - 测试所有组件的主题适配
  - 实现主题动画过渡效果
  - **对应需求**: 全局状态管理需求

## 测试和优化

### 16. 测试覆盖
- [ ] 16.1 创建组件单元测试
  - 为关键组件编写单元测试
  - 测试表单验证和用户交互
  - 实现状态管理测试
  - 添加工具函数测试
  - **对应需求**: 技术架构需求 9.6

- [ ] 16.2 实现集成测试
  - 创建页面级集成测试
  - 测试组件间交互逻辑
  - 实现模拟数据测试场景
  - 添加错误处理测试
  - **对应需求**: 技术架构需求 9.6

### 17. 性能优化和完善
- [ ] 17.1 实现代码分割和懒加载
  - 添加路由级代码分割
  - 实现大型组件懒加载
  - 优化第三方库打包
  - 测试加载性能提升
  - **对应需求**: 技术架构需求 9.7

- [ ] 17.2 最终集成和测试
  - 集成所有模块功能
  - 测试完整的用户流程
  - 修复发现的问题和 bug
  - 验证所有需求实现完整性
  - **对应需求**: 所有功能需求的集成验收

每个任务都明确指向具体的需求条目，确保实现的完整性和可追溯性。任务按照依赖关系排序，从基础架构到具体功能，最后到测试和优化，形成完整的开发流程。